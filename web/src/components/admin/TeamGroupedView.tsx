"use client";

import React, { useState, useMemo } from "react";
import { ChevronDownIcon, ChevronRightIcon } from "@/components/icons/icons";
import { UserTeams } from "@/lib/types";
import { FiSearch, FiFilter } from "react-icons/fi";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuCheckboxItem,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";

export interface TeamGroupedItem {
  id: string | number;
  user_teams?: number[];
  is_public?: boolean;
  [key: string]: any;
}

interface CollapsibleTeamSectionProps<T extends TeamGroupedItem> {
  team: UserTeams | null; // null for public section
  items: T[];
  isExpanded: boolean;
  onToggle: () => void;
  renderItem: (item: T, index: number) => React.ReactNode;
  emptyMessage?: string;
}

function CollapsibleTeamSection<T extends TeamGroupedItem>({
  team,
  items,
  isExpanded,
  onToggle,
  renderItem,
  emptyMessage = "No items found",
}: CollapsibleTeamSectionProps<T>) {
  const sectionTitle = team ? team.name : "Public";
  const itemCount = items.length;

  return (
    <div className="border border-border rounded-lg mb-4">
      {/* Section Header */}
      <div
        className="flex items-center justify-between p-4 cursor-pointer hover:bg-background-100 dark:hover:bg-neutral-800 transition-colors"
        onClick={onToggle}
      >
        <div className="flex items-center gap-2">
          {isExpanded ? (
            <ChevronDownIcon className="w-4 h-4 text-text-500" />
          ) : (
            <ChevronRightIcon className="w-4 h-4 text-text-500" />
          )}
          <h3 className="text-lg font-semibold text-text-900">
            {sectionTitle}
          </h3>
          <span className="text-sm text-text-500 bg-background-200 dark:bg-neutral-700 px-2 py-1 rounded-full">
            {itemCount} {itemCount === 1 ? "item" : "items"}
          </span>
        </div>
      </div>

      {/* Section Content */}
      {isExpanded && (
        <div className="border-t border-border">
          {items.length > 0 ? (
            <div className="p-4">
              {items.map((item, index) => (
                <div key={item.id} className="mb-2 last:mb-0">
                  {renderItem(item, index)}
                </div>
              ))}
            </div>
          ) : (
            <div className="p-4 text-center text-text-500">
              {emptyMessage}
            </div>
          )}
        </div>
      )}
    </div>
  );
}

interface TeamGroupedViewProps<T extends TeamGroupedItem> {
  items: T[];
  teams: UserTeams[];
  renderItem: (item: T, index: number) => React.ReactNode;
  emptyMessage?: string;
  defaultExpandedTeams?: number[]; // Team IDs that should be expanded by default
  showPublicSection?: boolean;
  enableSearch?: boolean; // Enable search functionality
  enableTeamFilter?: boolean; // Enable team filtering
  searchPlaceholder?: string; // Custom search placeholder
}

export function TeamGroupedView<T extends TeamGroupedItem>({
  items,
  teams,
  renderItem,
  emptyMessage = "No items found",
  defaultExpandedTeams = [],
  showPublicSection = true,
  enableSearch = false,
  enableTeamFilter = false,
  searchPlaceholder = "Search items...",
}: TeamGroupedViewProps<T>) {
  // State to track which sections are expanded
  const [expandedSections, setExpandedSections] = useState<Set<string>>(() => {
    const initialExpanded = new Set<string>();
    
    // Expand public section by default if it has items
    if (showPublicSection) {
      const publicItems = items.filter(item => item.is_public);
      if (publicItems.length > 0) {
        initialExpanded.add("public");
      }
    }
    
    // Expand specified teams by default
    defaultExpandedTeams.forEach(teamId => {
      initialExpanded.add(`team-${teamId}`);
    });
    
    return initialExpanded;
  });

  // Search and filter state
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedTeams, setSelectedTeams] = useState<Set<number>>(new Set(teams.map(t => t.id)));

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => {
      const newSet = new Set(prev);
      if (newSet.has(sectionId)) {
        newSet.delete(sectionId);
      } else {
        newSet.add(sectionId);
      }
      return newSet;
    });
  };

  // Filter items based on search and team selection
  const filteredItems = useMemo(() => {
    let filtered = items;

    // Apply search filter
    if (enableSearch && searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(item => {
        // Search in common properties that might exist
        const searchableText = [
          item.name,
          item.title,
          item.description,
          // Add more searchable fields as needed
        ].filter(Boolean).join(' ').toLowerCase();

        return searchableText.includes(query);
      });
    }

    // Apply team filter
    if (enableTeamFilter && selectedTeams.size < teams.length) {
      filtered = filtered.filter(item => {
        if (item.is_public && showPublicSection) {
          return true; // Always show public items
        }
        return item.user_teams?.some(teamId => selectedTeams.has(teamId)) || false;
      });
    }

    return filtered;
  }, [items, searchQuery, selectedTeams, enableSearch, enableTeamFilter, teams.length, showPublicSection]);

  // Group items by team
  const groupedItems = useMemo(() => {
    const groups: { [key: string]: T[] } = {};
    
    // Initialize groups for all teams
    teams.forEach(team => {
      groups[`team-${team.id}`] = [];
    });
    
    // Initialize public group if needed
    if (showPublicSection) {
      groups["public"] = [];
    }

    // Categorize filtered items
    filteredItems.forEach(item => {
      if (item.is_public && showPublicSection) {
        groups["public"].push(item);
      } else if (item.user_teams && item.user_teams.length > 0) {
        // Add item to each team it belongs to
        item.user_teams.forEach(teamId => {
          const groupKey = `team-${teamId}`;
          if (groups[groupKey]) {
            groups[groupKey].push(item);
          }
        });
      } else {
        // Items with no team assignment - add to public if available
        if (showPublicSection) {
          groups["public"].push(item);
        }
      }
    });

    return groups;
  }, [filteredItems, teams, showPublicSection]);

  // Get teams that have items
  const teamsWithItems = teams.filter(team => 
    groupedItems[`team-${team.id}`] && groupedItems[`team-${team.id}`].length > 0
  );

  const publicItems = showPublicSection ? groupedItems["public"] || [] : [];

  if (items.length === 0) {
    return (
      <div className="text-center py-8 text-text-500">
        {emptyMessage}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Search and Filter Controls */}
      {(enableSearch || enableTeamFilter) && (
        <div className="flex flex-col sm:flex-row gap-4 p-4 bg-background-50 dark:bg-neutral-900 rounded-lg border border-border">
          {/* Search Input */}
          {enableSearch && (
            <div className="flex-1">
              <div className="relative">
                <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-text-500" />
                <Input
                  type="text"
                  placeholder={searchPlaceholder}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          )}

          {/* Team Filter */}
          {enableTeamFilter && teams.length > 0 && (
            <div className="flex items-center gap-2">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="gap-2">
                    <FiFilter className="h-4 w-4" />
                    Teams
                    {selectedTeams.size < teams.length && (
                      <Badge variant="secondary" className="ml-1">
                        {selectedTeams.size}/{teams.length}
                      </Badge>
                    )}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56">
                  <DropdownMenuLabel>Filter by Teams</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuCheckboxItem
                    checked={selectedTeams.size === teams.length}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        setSelectedTeams(new Set(teams.map(t => t.id)));
                      } else {
                        setSelectedTeams(new Set());
                      }
                    }}
                  >
                    All Teams
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuSeparator />
                  {teams.map(team => (
                    <DropdownMenuCheckboxItem
                      key={team.id}
                      checked={selectedTeams.has(team.id)}
                      onCheckedChange={(checked) => {
                        const newSelected = new Set(selectedTeams);
                        if (checked) {
                          newSelected.add(team.id);
                        } else {
                          newSelected.delete(team.id);
                        }
                        setSelectedTeams(newSelected);
                      }}
                    >
                      {team.name}
                    </DropdownMenuCheckboxItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          )}
        </div>
      )}
      {/* Public Section */}
      {showPublicSection && publicItems.length > 0 && (
        <CollapsibleTeamSection
          team={null}
          items={publicItems}
          isExpanded={expandedSections.has("public")}
          onToggle={() => toggleSection("public")}
          renderItem={renderItem}
          emptyMessage={emptyMessage}
        />
      )}

      {/* Team Sections */}
      {teamsWithItems.map(team => {
        const sectionId = `team-${team.id}`;
        const teamItems = groupedItems[sectionId] || [];

        return (
          <CollapsibleTeamSection
            key={team.id}
            team={team}
            items={teamItems}
            isExpanded={expandedSections.has(sectionId)}
            onToggle={() => toggleSection(sectionId)}
            renderItem={renderItem}
            emptyMessage={emptyMessage}
          />
        );
      })}

      {/* Show message if no items in any section */}
      {teamsWithItems.length === 0 && publicItems.length === 0 && (
        <div className="text-center py-8 text-text-500">
          {emptyMessage}
        </div>
      )}
    </div>
  );
}

// Utility function to create team-aware render functions
export function createTeamAwareRenderer<T extends TeamGroupedItem>(
  originalRenderer: (item: T, index: number) => React.ReactNode,
  teams: UserTeams[]
) {
  const teamMap = new Map(teams.map(team => [team.id, team.name]));

  return (item: T, index: number) => {
    const teamNames = item.user_teams?.map(teamId => teamMap.get(teamId)).filter(Boolean) || [];
    const isPublic = item.is_public;

    return (
      <div className="space-y-2">
        {originalRenderer(item, index)}
        {/* Optional: Show team badges */}
        {(teamNames.length > 0 || isPublic) && (
          <div className="flex flex-wrap gap-1 mt-1">
            {isPublic && (
              <span className="text-xs bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded-full">
                Public
              </span>
            )}
            {teamNames.map(teamName => (
              <span
                key={teamName}
                className="text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded-full"
              >
                {teamName}
              </span>
            ))}
          </div>
        )}
      </div>
    );
  };
}
