import React, { useState, useMemo, useEffect, useRef } from "react";
import {
  Table,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
  TableHeader,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { IndexAttemptStatus } from "@/components/Status";
import { timeAgo } from "@/lib/time";
import {
  ConnectorIndexingStatus,
  ConnectorSummary,
  GroupedConnectorSummaries,
  ValidSources,
  UserRole,
} from "@/lib/types";
import { useRouter } from "next/navigation";
import {
  FiChevronDown,
  FiChevronRight,
  FiSettings,
  FiLock,
  FiUnlock,
  FiRefreshCw,
  FiPauseCircle,
  FiUsers,
} from "react-icons/fi";

import { SourceIcon } from "@/components/SourceIcon";
import { getSourceDisplayName } from "@/lib/sources";
import { CustomTooltip } from "@/components/tooltip/CustomTooltip";
import { Warning } from "@phosphor-icons/react";
import { usePaidEnterpriseFeaturesEnabled } from "@/components/settings/usePaidEnterpriseFeaturesEnabled";
import { ConnectorCredentialPairStatus } from "../../connector/[ccPairId]/types";
import { useUser } from "@/components/user/UserProvider";
import { useUserTeams } from "@/lib/hooks";

// Types for team-based grouping
type TeamSummary = {
  count: number;
  active: number;
  public: number;
  totalDocsIndexed: number;
  errors: number;
};

type TeamGroupedConnectors = {
  [teamId: string]: {
    [source in ValidSources]?: ConnectorIndexingStatus<any, any>[];
  };
};

type TeamGroupedSummaries = {
  [teamId: string]: {
    teamSummary: TeamSummary;
    sourceSummaries: GroupedConnectorSummaries;
  };
};

function TeamSummaryRow({
  teamName,
  summary,
  isOpen,
  onToggle,
}: {
  teamName: string;
  summary: TeamSummary;
  isOpen: boolean;
  onToggle: () => void;
}) {
  const isPaidEnterpriseFeaturesEnabled = usePaidEnterpriseFeaturesEnabled();

  return (
    <TableRow
      onClick={onToggle}
      className="border border-neutral-300 dark:border-neutral-600 hover:bg-neutral-100 dark:hover:bg-neutral-800/50 bg-neutral-50 dark:bg-neutral-900/30 py-6 rounded-lg cursor-pointer shadow-sm hover:shadow-md transition-all duration-200"
    >
      <TableCell>
        <div className="text-xl flex items-center truncate ellipsis gap-x-3 font-semibold">
          <div className="cursor-pointer p-1 rounded-full hover:bg-neutral-200 dark:hover:bg-neutral-700 transition-colors">
            {isOpen ? (
              <FiChevronDown size={20} className="text-neutral-600 dark:text-neutral-400" />
            ) : (
              <FiChevronRight size={20} className="text-neutral-600 dark:text-neutral-400" />
            )}
          </div>
          <div className="flex items-center gap-2">
            <FiUsers size={20} className="text-neutral-600 dark:text-neutral-400" />
            <span className="text-neutral-800 dark:text-neutral-200">{teamName}</span>
          </div>
        </div>
      </TableCell>

      <TableCell>
        <div className="text-sm text-neutral-500 dark:text-neutral-300">
          Total Connectors
        </div>
        <div className="text-xl font-semibold">{summary.count}</div>
      </TableCell>

      <TableCell>
        <div className="text-sm text-neutral-500 dark:text-neutral-300">
          Active Connectors
        </div>
        <p className="flex text-xl mx-auto font-semibold items-center text-lg mt-1">
          {summary.active}/{summary.count}
        </p>
      </TableCell>

      {isPaidEnterpriseFeaturesEnabled && (
        <TableCell>
          <div className="text-sm text-neutral-500 dark:text-neutral-300">
            Public Connectors
          </div>
          <p className="flex text-xl mx-auto font-semibold items-center text-lg mt-1">
            {summary.public}/{summary.count}
          </p>
        </TableCell>
      )}

      <TableCell>
        <div className="text-sm text-neutral-500 dark:text-neutral-300">
          Total Docs Indexed
        </div>
        <div className="text-xl font-semibold">
          {summary.totalDocsIndexed.toLocaleString()}
        </div>
      </TableCell>

      <TableCell>
        <div className="text-sm text-neutral-500 dark:text-neutral-300">
          Errors
        </div>

        <div className="flex items-center text-lg gap-x-1 font-semibold">
          {summary.errors > 0 && <Warning className="text-error h-6 w-6" />}
          {summary.errors}
        </div>
      </TableCell>

      <TableCell />
    </TableRow>
  );
}

function SummaryRow({
  source,
  summary,
  isOpen,
  onToggle,
}: {
  source: ValidSources;
  summary: ConnectorSummary;
  isOpen: boolean;
  onToggle: () => void;
}) {
  const isPaidEnterpriseFeaturesEnabled = usePaidEnterpriseFeaturesEnabled();

  return (
    <TableRow
      onClick={onToggle}
      className="border-border dark:hover:bg-neutral-800 dark:border-neutral-700 group hover:bg-background-settings-hover/20 bg-background-sidebar py-4 rounded-sm !border cursor-pointer"
    >
      <TableCell>
        <div className="text-xl flex items-center truncate ellipsis gap-x-2 font-semibold">
          <div className="cursor-pointer">
            {isOpen ? (
              <FiChevronDown size={20} />
            ) : (
              <FiChevronRight size={20} />
            )}
          </div>
          <SourceIcon iconSize={20} sourceType={source} />
          {getSourceDisplayName(source)}
        </div>
      </TableCell>

      <TableCell>
        <div className="text-sm text-neutral-500 dark:text-neutral-300">
          Total Connectors
        </div>
        <div className="text-xl font-semibold">{summary.count}</div>
      </TableCell>

      <TableCell>
        <div className="text-sm text-neutral-500 dark:text-neutral-300">
          Active Connectors
        </div>
        <p className="flex text-xl mx-auto font-semibold items-center text-lg mt-1">
          {summary.active}/{summary.count}
        </p>
      </TableCell>

      {isPaidEnterpriseFeaturesEnabled && (
        <TableCell>
          <div className="text-sm text-neutral-500 dark:text-neutral-300">
            Public Connectors
          </div>
          <p className="flex text-xl mx-auto font-semibold items-center text-lg mt-1">
            {summary.public}/{summary.count}
          </p>
        </TableCell>
      )}

      <TableCell>
        <div className="text-sm text-neutral-500 dark:text-neutral-300">
          Total Docs Indexed
        </div>
        <div className="text-xl font-semibold">
          {summary.totalDocsIndexed.toLocaleString()}
        </div>
      </TableCell>

      <TableCell>
        <div className="text-sm text-neutral-500 dark:text-neutral-300">
          Errors
        </div>

        <div className="flex items-center text-lg gap-x-1 font-semibold">
          {summary.errors > 0 && <Warning className="text-error h-6 w-6" />}
          {summary.errors}
        </div>
      </TableCell>

      <TableCell />
    </TableRow>
  );
}

function ConnectorRow({
  ccPairsIndexingStatus,
  invisible,
  isEditable,
}: {
  ccPairsIndexingStatus: ConnectorIndexingStatus<any, any>;
  invisible?: boolean;
  isEditable: boolean;
}) {
  const router = useRouter();
  const isPaidEnterpriseFeaturesEnabled = usePaidEnterpriseFeaturesEnabled();

  const handleManageClick = (e: any) => {
    e.stopPropagation();
    router.push(`/admin/connector/${ccPairsIndexingStatus.cc_pair_id}`);
  };

  const getActivityBadge = () => {
    if (
      ccPairsIndexingStatus.cc_pair_status !==
      ConnectorCredentialPairStatus.ACTIVE
    ) {
      return (
        <Badge variant="outline">
          {ccPairsIndexingStatus.cc_pair_status === "PAUSED"
            ? "Paused"
            : ccPairsIndexingStatus.cc_pair_status}
        </Badge>
      );
    }

    // ACTIVE case
    switch (ccPairsIndexingStatus.last_status) {
      case "in_progress":
        return (
          <Badge circle variant="success">
            Indexing
          </Badge>
        );
      case "not_started":
        return (
          <Badge circle variant="not_started">
            Scheduled
          </Badge>
        );
      default:
        return (
          <Badge circle variant="success">
            Active
          </Badge>
        );
    }
  };

  return (
    <TableRow
      className={`
border border-border dark:border-neutral-700
        hover:bg-accent-background ${
          invisible
            ? "invisible !h-0 !-mb-10 !border-none"
            : "!border border-border dark:border-neutral-700"
        }  w-full cursor-pointer relative `}
      onClick={() => {
        router.push(`/admin/connector/${ccPairsIndexingStatus.cc_pair_id}`);
      }}
    >
      <TableCell className="">
        <p className="lg:w-[200px] xl:w-[400px] inline-block ellipsis truncate">
          {ccPairsIndexingStatus.name}
        </p>
      </TableCell>
      <TableCell>
        {timeAgo(ccPairsIndexingStatus?.last_success) || "-"}
      </TableCell>
      <TableCell>{getActivityBadge()}</TableCell>
      {isPaidEnterpriseFeaturesEnabled && (
        <TableCell>
          {ccPairsIndexingStatus.access_type === "public" ? (
            <Badge variant={isEditable ? "success" : "default"} icon={FiUnlock}>
              Public
            </Badge>
          ) : ccPairsIndexingStatus.access_type === "sync" ? (
            <Badge
              variant={isEditable ? "auto-sync" : "default"}
              icon={FiRefreshCw}
            >
              Auto-Sync
            </Badge>
          ) : (
            <Badge variant={isEditable ? "private" : "default"} icon={FiLock}>
              Private
            </Badge>
          )}
        </TableCell>
      )}
      <TableCell>
        {ccPairsIndexingStatus.docs_indexed.toLocaleString()}
      </TableCell>
      <TableCell>
        <IndexAttemptStatus
          status={ccPairsIndexingStatus?.last_finished_status || "not_started"}
        />
      </TableCell>
      <TableCell>
        <div className="flex">
          {isEditable && (
            <CustomTooltip content="Manage Connector">
              <div
                onClick={handleManageClick}
                className="cursor-pointer mx-auto flex"
              >
                <FiSettings size={16} />
              </div>
            </CustomTooltip>
          )}
        </div>
      </TableCell>
    </TableRow>
  );
}

export function AdminCCPairIndexingStatusTable({
  ccPairsIndexingStatuses,
  editableCcPairsIndexingStatuses,
}: {
  ccPairsIndexingStatuses: ConnectorIndexingStatus<any, any>[];
  editableCcPairsIndexingStatuses: ConnectorIndexingStatus<any, any>[];
}) {
  const [searchTerm, setSearchTerm] = useState("");
  const { user } = useUser();
  const { data: userTeams } = useUserTeams();

  const searchInputRef = useRef<HTMLInputElement>(null);
  const isPaidEnterpriseFeaturesEnabled = usePaidEnterpriseFeaturesEnabled();

  // State for team-level collapsibles
  const [teamsToggled, setTeamsToggled] = useState<Record<string, boolean>>({});

  // State for source-level collapsibles within teams (teamId_source format)
  const [teamSourcesToggled, setTeamSourcesToggled] = useState<Record<string, boolean>>({});

  // Only show admin view for admin users
  if (user?.role !== UserRole.ADMIN) {
    return <div>Access denied. Admin view only.</div>;
  }

  // Apply role-based filtering - admin sees all connectors
  const filteredCcPairsIndexingStatuses = ccPairsIndexingStatuses;
  const filteredEditableCcPairsIndexingStatuses = editableCcPairsIndexingStatuses;

  useEffect(() => {
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, []);

  // Create team-based grouping
  const { teamGroupedData, sortedTeamIds } = useMemo(() => {
    const teamGroups: TeamGroupedConnectors = {};
    const teamSummaries: TeamGroupedSummaries = {};

    // Combine all connectors
    const allConnectors = [...filteredEditableCcPairsIndexingStatuses];
    filteredCcPairsIndexingStatuses.forEach((status) => {
      if (!filteredEditableCcPairsIndexingStatuses.some(
        (e) => e.cc_pair_id === status.cc_pair_id
      )) {
        allConnectors.push(status);
      }
    });

    // Group connectors by team
    allConnectors.forEach((connector) => {
      const source = connector.connector.source;

      if (connector.access_type === "public" || !connector.groups || connector.groups.length === 0) {
        // Public connectors go to "Public" group
        const teamKey = "public";
        if (!teamGroups[teamKey]) {
          teamGroups[teamKey] = {};
        }
        if (!teamGroups[teamKey][source]) {
          teamGroups[teamKey][source] = [];
        }
        teamGroups[teamKey][source]!.push(connector);
      } else {
        // Private connectors go to their assigned teams
        connector.groups.forEach((teamId) => {
          const teamKey = teamId.toString();
          if (!teamGroups[teamKey]) {
            teamGroups[teamKey] = {};
          }
          if (!teamGroups[teamKey][source]) {
            teamGroups[teamKey][source] = [];
          }
          teamGroups[teamKey][source]!.push(connector);
        });
      }
    });

    // Calculate summaries for each team
    Object.keys(teamGroups).forEach((teamKey) => {
      const teamConnectors = teamGroups[teamKey];
      const sourceSummaries: GroupedConnectorSummaries = {} as GroupedConnectorSummaries;

      let teamTotalCount = 0;
      let teamTotalActive = 0;
      let teamTotalPublic = 0;
      let teamTotalDocs = 0;
      let teamTotalErrors = 0;

      Object.keys(teamConnectors).forEach((source) => {
        const connectors = teamConnectors[source as ValidSources] || [];
        sourceSummaries[source as ValidSources] = {
          count: connectors.length,
          active: connectors.filter(
            (status) => status.cc_pair_status === ConnectorCredentialPairStatus.ACTIVE
          ).length,
          public: connectors.filter((status) => status.access_type === "public").length,
          totalDocsIndexed: connectors.reduce((sum, status) => sum + status.docs_indexed, 0),
          errors: connectors.filter((status) => status.last_finished_status === "failed").length,
        };

        teamTotalCount += sourceSummaries[source as ValidSources].count;
        teamTotalActive += sourceSummaries[source as ValidSources].active;
        teamTotalPublic += sourceSummaries[source as ValidSources].public;
        teamTotalDocs += sourceSummaries[source as ValidSources].totalDocsIndexed;
        teamTotalErrors += sourceSummaries[source as ValidSources].errors;
      });

      teamSummaries[teamKey] = {
        teamSummary: {
          count: teamTotalCount,
          active: teamTotalActive,
          public: teamTotalPublic,
          totalDocsIndexed: teamTotalDocs,
          errors: teamTotalErrors,
        },
        sourceSummaries,
      };
    });

    // Sort team IDs: "public" first, then by team name
    const sortedIds = Object.keys(teamGroups).sort((a, b) => {
      if (a === "public") return -1;
      if (b === "public") return 1;

      const teamNameA = userTeams?.find(t => t.id.toString() === a)?.name || `Team ${a}`;
      const teamNameB = userTeams?.find(t => t.id.toString() === b)?.name || `Team ${b}`;
      return teamNameA.localeCompare(teamNameB);
    });

    return {
      teamGroupedData: {
        teamGroups,
        teamSummaries,
      },
      sortedTeamIds: sortedIds,
    };
  }, [filteredCcPairsIndexingStatuses, filteredEditableCcPairsIndexingStatuses, userTeams]);

  // Toggle functions
  const toggleTeam = (teamId: string) => {
    setTeamsToggled(prev => ({
      ...prev,
      [teamId]: !prev[teamId]
    }));
  };

  const toggleTeamSource = (teamId: string, source: ValidSources) => {
    const key = `${teamId}_${source}`;
    setTeamSourcesToggled(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  // Expand/Collapse all teams
  const shouldExpandTeams = useMemo(() => {
    return sortedTeamIds.some(teamId => !teamsToggled[teamId]);
  }, [teamsToggled, sortedTeamIds]);

  const toggleAllTeams = () => {
    const newState: Record<string, boolean> = {};
    sortedTeamIds.forEach(teamId => {
      newState[teamId] = shouldExpandTeams;
    });
    setTeamsToggled(newState);
  };

  return (
    <Table className="w-full">
      <TableHeader>
        <TableRow noHover className="border border-border dark:border-neutral-700">
          <TableHead>Team / Connector Type</TableHead>
          <TableHead>Total Connectors</TableHead>
          <TableHead>Active Connectors</TableHead>
          {isPaidEnterpriseFeaturesEnabled && (
            <TableHead>Public Connectors</TableHead>
          )}
          <TableHead>Total Docs Indexed</TableHead>
          <TableHead>Errors</TableHead>
          <TableHead></TableHead>
        </TableRow>
      </TableHeader>
      <div className="flex -mt-12 items-center w-0 m4 gap-x-2">
        <input
          type="text"
          ref={searchInputRef}
          placeholder="Search connectors..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="ml-1 w-96 h-9  border border-border flex-none rounded-md bg-background-50 px-3 py-1 text-sm shadow-sm transition-colors placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring"
        />

        <Button className="h-9" onClick={() => toggleAllTeams()}>
          {!shouldExpandTeams ? "Collapse All" : "Expand All"}
        </Button>
      </div>
      <TableBody>
        {sortedTeamIds.map((teamId, teamIndex) => {
          const teamData = teamGroupedData.teamSummaries[teamId];
          const teamConnectors = teamGroupedData.teamGroups[teamId];

          if (!teamData || teamData.teamSummary.count === 0) {
            return null;
          }

          // Get team name
          const teamName = teamId === "public"
            ? "Public"
            : userTeams?.find(t => t.id.toString() === teamId)?.name || `Team ${teamId}`;

          // Check if team matches search or has matching connectors
          const teamMatches = teamName.toLowerCase().includes(searchTerm.toLowerCase());

          // Get all connectors in this team that match search
          const allTeamConnectors: ConnectorIndexingStatus<any, any>[] = [];
          Object.values(teamConnectors).forEach(sourceConnectors => {
            if (sourceConnectors) {
              allTeamConnectors.push(...sourceConnectors);
            }
          });

          const matchingTeamConnectors = allTeamConnectors.filter(
            (status) =>
              (status.name || "")
                .toLowerCase()
                .includes(searchTerm.toLowerCase())
          );

          if (!teamMatches && matchingTeamConnectors.length === 0) {
            return null;
          }

          const isTeamOpen = teamsToggled[teamId] || false;

          return (
            <React.Fragment key={teamIndex}>
              {teamIndex > 0 && <tr><td colSpan={7} className="h-6"></td></tr>}
              <TeamSummaryRow
                teamName={teamName}
                summary={teamData.teamSummary}
                isOpen={isTeamOpen}
                onToggle={() => toggleTeam(teamId)}
              />

              {isTeamOpen && (
                <>
                  {/* Team boundary container with subtle visual separation */}
                  <tr>
                    <td colSpan={7} className="p-0">
                      <div className="mx-2 sm:mx-4 my-4 border border-dashed border-neutral-300 dark:border-neutral-600 rounded-lg bg-neutral-50/50 dark:bg-neutral-900/20 p-3 sm:p-4">
                        <div className="space-y-3">
                          {/* Team label */}
                          <div className="flex items-center gap-2 mb-3">
                            <div className="h-0.5 w-6 bg-neutral-400 dark:bg-neutral-500 rounded"></div>
                            <span className="text-xs font-medium text-neutral-600 dark:text-neutral-400 uppercase tracking-wide">
                              {teamName} Connectors
                            </span>
                            <div className="h-0.5 flex-1 bg-neutral-300 dark:bg-neutral-600 rounded"></div>
                          </div>

                          {/* Render sources within this team */}
                          {Object.keys(teamConnectors)
                            .filter(source => source !== "not_applicable" && source !== "ingestion_api")
                            .sort()
                            .map((source, sourceIndex) => {
                              const sourceConnectors = teamConnectors[source as ValidSources] || [];
                              const sourceSummary = teamData.sourceSummaries[source as ValidSources];

                              if (!sourceSummary || sourceSummary.count === 0) {
                                return null;
                              }

                              const sourceMatches = source.toLowerCase().includes(searchTerm.toLowerCase());
                              const matchingSourceConnectors = sourceConnectors.filter(
                                (status) =>
                                  (status.name || "")
                                    .toLowerCase()
                                    .includes(searchTerm.toLowerCase())
                              );

                              if (!teamMatches && !sourceMatches && matchingSourceConnectors.length === 0) {
                                return null;
                              }

                              const teamSourceKey = `${teamId}_${source}`;
                              const isSourceOpen = teamSourcesToggled[teamSourceKey] || false;

                              return (
                                <div key={`${teamId}_${source}_${sourceIndex}`} className="mb-3 last:mb-0">
                                  {/* Connector type section with original large styling */}
                                  <Table className="w-full">
                                    <TableBody>
                                      <SummaryRow
                                        source={source as ValidSources}
                                        summary={sourceSummary}
                                        isOpen={isSourceOpen}
                                        onToggle={() => toggleTeamSource(teamId, source as ValidSources)}
                                      />

                                      {isSourceOpen && (
                                        <>
                                          <TableRow
                                            noHover
                                            className="border border-border dark:border-neutral-700"
                                          >
                                            <TableHead>Name</TableHead>
                                            <TableHead>Last Indexed</TableHead>
                                            <TableHead>Activity</TableHead>
                                            {isPaidEnterpriseFeaturesEnabled && (
                                              <TableHead>Permissions</TableHead>
                                            )}
                                            <TableHead>Total Docs</TableHead>
                                            <TableHead>Last Status</TableHead>
                                            <TableHead></TableHead>
                                          </TableRow>

                                          {(teamMatches || sourceMatches
                                            ? sourceConnectors
                                            : matchingSourceConnectors
                                          ).map((ccPairsIndexingStatus) => (
                                            <ConnectorRow
                                              key={ccPairsIndexingStatus.cc_pair_id}
                                              ccPairsIndexingStatus={ccPairsIndexingStatus}
                                              isEditable={filteredEditableCcPairsIndexingStatuses.some(
                                                (e) =>
                                                  e.cc_pair_id === ccPairsIndexingStatus.cc_pair_id
                                              )}
                                            />
                                          ))}
                                        </>
                                      )}
                                    </TableBody>
                                  </Table>
                                </div>
                              );
                            })}
                        </div>
                      </div>
                    </td>
                  </tr>
                </>
              )}
            </React.Fragment>
          );
        })}
      </TableBody>
    </Table>
  );
}
