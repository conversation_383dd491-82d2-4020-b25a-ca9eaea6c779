"use client";

import React, { useState, useMemo } from "react";
import { Badge } from "@/components/ui/badge";
import { IndexAttemptStatus } from "@/components/Status";
import { timeAgo } from "@/lib/time";
import {
  ConnectorIndexingStatus,
  UserRole,
} from "@/lib/types";
import { useRouter } from "next/navigation";
import {
  FiSettings,
  FiLock,
  FiUnlock,
  FiRefreshCw,
} from "react-icons/fi";
import { SourceIcon } from "@/components/SourceIcon";
import { getSourceDisplayName } from "@/lib/sources";
import { CustomTooltip } from "@/components/tooltip/CustomTooltip";
import { usePaidEnterpriseFeaturesEnabled } from "@/components/settings/usePaidEnterpriseFeaturesEnabled";
import { ConnectorCredentialPairStatus } from "../../connector/[ccPairId]/types";
import { useUser } from "@/components/user/UserProvider";
import { TeamGroupedView, TeamGroupedItem } from "@/components/admin/TeamGroupedView";
import { useTeamGroupedData } from "@/hooks/useTeamGroupedData";

// Extend ConnectorIndexingStatus to match TeamGroupedItem interface
interface ConnectorWithTeamData extends ConnectorIndexingStatus<any, any>, TeamGroupedItem {
  id: number; // Override to resolve type conflict - using cc_pair_id as id
  user_teams: number[]; // Map groups to user_teams for consistency
  is_public: boolean; // Override to resolve type conflict - derived from access_type
}

function ConnectorRow({
  connector,
  isEditable,
}: {
  connector: ConnectorWithTeamData;
  isEditable: boolean;
}) {
  const router = useRouter();
  const isPaidEnterpriseFeaturesEnabled = usePaidEnterpriseFeaturesEnabled();

  const handleManageClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    router.push(`/admin/connector/${connector.cc_pair_id}`);
  };

  const getActivityBadge = () => {
    if (connector.cc_pair_status === ConnectorCredentialPairStatus.PAUSED) {
      return (
        <Badge variant="outline" className="text-orange-600 border-orange-600">
          Paused
        </Badge>
      );
    } else if (connector.cc_pair_status === ConnectorCredentialPairStatus.ACTIVE) {
      return (
        <Badge variant="success">
          Active
        </Badge>
      );
    } else {
      return (
        <Badge variant="destructive">
          {connector.cc_pair_status}
        </Badge>
      );
    }
  };

  return (
    <div 
      className="border border-border rounded-lg p-4 bg-background-50 dark:bg-neutral-900 hover:bg-accent-background cursor-pointer"
      onClick={() => router.push(`/admin/connector/${connector.cc_pair_id}`)}
    >
      <div className="grid grid-cols-1 lg:grid-cols-6 gap-4 items-center">
        {/* Name and Source */}
        <div className="lg:col-span-2 flex items-center gap-3">
          <SourceIcon iconSize={20} sourceType={connector.connector.source} />
          <div>
            <p className="font-medium text-text-900 truncate">
              {connector.name || `${getSourceDisplayName(connector.connector.source)} Connector`}
            </p>
            <p className="text-sm text-text-500">
              {getSourceDisplayName(connector.connector.source)}
            </p>
          </div>
        </div>

        {/* Last Success */}
        <div className="text-center">
          <p className="text-sm text-text-500">Last Success</p>
          <p className="text-sm font-medium">
            {timeAgo(connector.last_success) || "-"}
          </p>
        </div>

        {/* Status */}
        <div className="text-center">
          <p className="text-sm text-text-500">Status</p>
          <div className="flex justify-center">
            {getActivityBadge()}
          </div>
        </div>

        {/* Access Type */}
        {isPaidEnterpriseFeaturesEnabled && (
          <div className="text-center">
            <p className="text-sm text-text-500">Access</p>
            <div className="flex justify-center">
              {connector.access_type === "public" ? (
                <Badge variant={isEditable ? "success" : "default"} icon={FiUnlock}>
                  Public
                </Badge>
              ) : connector.access_type === "sync" ? (
                <Badge
                  variant={isEditable ? "auto-sync" : "default"}
                  icon={FiRefreshCw}
                >
                  Auto-Sync
                </Badge>
              ) : (
                <Badge variant={isEditable ? "private" : "default"} icon={FiLock}>
                  Private
                </Badge>
              )}
            </div>
          </div>
        )}

        {/* Docs Indexed and Actions */}
        <div className="flex items-center justify-between">
          <div className="text-center">
            <p className="text-sm text-text-500">Docs</p>
            <p className="text-sm font-medium">{connector.docs_indexed}</p>
          </div>
          
          <div className="flex items-center gap-2">
            <IndexAttemptStatus
              status={connector.last_finished_status || null}
              errorMsg={connector?.latest_index_attempt?.error_msg}
            />
            
            {isEditable && (
              <CustomTooltip content="Manage Connector">
                <FiSettings
                  className="cursor-pointer text-text-500 hover:text-text-700"
                  onClick={handleManageClick}
                />
              </CustomTooltip>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export function TeamGroupedConnectorTable({
  ccPairsIndexingStatuses,
  editableCcPairsIndexingStatuses,
}: {
  ccPairsIndexingStatuses: ConnectorIndexingStatus<any, any>[];
  editableCcPairsIndexingStatuses: ConnectorIndexingStatus<any, any>[];
}) {
  const { user } = useUser();

  // Create a set of editable connector IDs for quick lookup
  const editableConnectorIds = useMemo(() => {
    return new Set(editableCcPairsIndexingStatuses.map(c => c.cc_pair_id));
  }, [editableCcPairsIndexingStatuses]);

  // Filter connectors based on user role (same logic as original)
  const filteredConnectors = useMemo(() => {
    let filtered = ccPairsIndexingStatuses;
    
    if (user?.role === UserRole.TEAM_ADMIN) {
      // TEAM_ADMIN users should not see public connectors
      filtered = ccPairsIndexingStatuses.filter(connector => connector.access_type !== "public");
    }
    
    return filtered;
  }, [ccPairsIndexingStatuses, user?.role]);

  // Convert connectors to match TeamGroupedItem interface
  const connectorsWithTeamData: ConnectorWithTeamData[] = useMemo(() => {
    return filteredConnectors.map(connector => ({
      ...connector,
      // Map groups to user_teams for consistency with TeamGroupedItem interface
      id: connector.cc_pair_id, 
      user_teams: connector.groups || [],
      is_public: connector.access_type === "public",
    }));
  }, [filteredConnectors]);

  // Use the team grouping hook
  const { publicItems, teamGroups, teamsMap, isLoading: teamsLoading } = useTeamGroupedData(connectorsWithTeamData);

  // Render function for individual connector items
  const renderConnectorItem = (connector: ConnectorWithTeamData, index: number) => {
    const isEditable = editableConnectorIds.has(connector.cc_pair_id);
    
    return (
      <ConnectorRow
        key={connector.cc_pair_id}
        connector={connector}
        isEditable={isEditable}
      />
    );
  };

  if (teamsLoading) {
    return <div className="text-center py-8">Loading teams...</div>;
  }

  return (
    <TeamGroupedView
      items={connectorsWithTeamData}
      teams={Array.from(teamsMap.values())}
      renderItem={renderConnectorItem}
      emptyMessage="No connectors found"
      showPublicSection={user?.role === UserRole.ADMIN} // Only show public section for admins
      defaultExpandedTeams={[]} // Start with all sections collapsed
      enableSearch={true}
      enableTeamFilter={true}
      searchPlaceholder="Search connectors by name or source..."
    />
  );
}
