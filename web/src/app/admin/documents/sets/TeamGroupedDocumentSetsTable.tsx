"use client";

import React, { useMemo } from "react";
import { Badge } from "@/components/ui/badge";
import { DocumentSet, UserRole } from "@/lib/types";
import { useRouter } from "next/navigation";
import {
  FiCheckCircle,
  FiClock,
  FiAlertTriangle,
  FiEdit2,
  FiLock,
  FiUnlock,
} from "react-icons/fi";
import { ConnectorTitle } from "@/components/admin/connectors/ConnectorTitle";
import { deleteDocumentSet } from "./lib";
import { PopupSpec } from "@/components/admin/connectors/Popup";
import { DeleteButton } from "@/components/DeleteButton";
import { useUser } from "@/components/user/UserProvider";
import { TeamGroupedView, TeamGroupedItem } from "@/components/admin/TeamGroupedView";
import { useTeamGroupedData } from "@/hooks/useTeamGroupedData";
import {
  <PERSON><PERSON><PERSON>,
  Toolt<PERSON>Content,
  <PERSON><PERSON><PERSON><PERSON>rovider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { InfoIcon } from "@/components/icons/icons";

// Extend DocumentSet to match TeamGroupedItem interface
interface DocumentSetWithTeamData extends DocumentSet, TeamGroupedItem {
  id: number; // Override to resolve type conflict between DocumentSet.id (number) and TeamGroupedItem.id (string | number)
  is_public: boolean; // Override to resolve type conflict between DocumentSet.is_public (boolean) and TeamGroupedItem.is_public (boolean | undefined)
  user_teams: number[]; // Override to resolve type conflict between DocumentSet.user_teams (number[]) and TeamGroupedItem.user_teams (number[] | undefined)
}

function DocumentSetRow({
  documentSet,
  isEditable,
  setPopup,
  refresh,
  refreshEditable,
}: {
  documentSet: DocumentSetWithTeamData;
  isEditable: boolean;
  setPopup: (popupSpec: PopupSpec | null) => void;
  refresh: () => void;
  refreshEditable: () => void;
}) {
  const router = useRouter();

  const handleEdit = () => {
    if (documentSet.is_up_to_date) {
      router.push(`/admin/documents/sets/${documentSet.id}`);
    }
  };

  return (
    <div className="border border-border rounded-lg p-4 bg-background-50 dark:bg-neutral-900">
      <div className="grid grid-cols-1 lg:grid-cols-5 gap-4">
        {/* Name Column */}
        <div className="flex items-center">
          {isEditable ? (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div
                    className={`
                      flex items-center gap-2 font-medium text-text-900 select-none
                      ${documentSet.is_up_to_date ? "cursor-pointer hover:bg-accent-background p-2 rounded" : "cursor-default"}
                    `}
                    onClick={handleEdit}
                  >
                    <FiEdit2 className="flex-shrink-0" />
                    <span className="break-words">{documentSet.name}</span>
                  </div>
                </TooltipTrigger>
                {!documentSet.is_up_to_date && (
                  <TooltipContent>
                    <div className="flex items-start gap-2">
                      <InfoIcon className="mt-0.5 flex-shrink-0" />
                      <span>
                        Cannot update while syncing! Wait for the sync to finish, then try again.
                      </span>
                    </div>
                  </TooltipContent>
                )}
              </Tooltip>
            </TooltipProvider>
          ) : (
            <div className="font-medium text-text-900 break-words">
              {documentSet.name}
            </div>
          )}
        </div>

        {/* Connectors Column */}
        <div className="lg:col-span-2">
          <div className="space-y-2">
            {documentSet.cc_pair_descriptors.map((ccPairDescriptor) => (
              <div key={ccPairDescriptor.id}>
                <ConnectorTitle
                  connector={ccPairDescriptor.connector}
                  ccPairName={ccPairDescriptor.name}
                  ccPairId={ccPairDescriptor.id}
                  showMetadata={false}
                />
              </div>
            ))}
          </div>
        </div>

        {/* Status Column */}
        <div className="text-center">
          <p className="text-sm text-text-500 mb-1">Status</p>
          <div className="flex justify-center">
            {documentSet.is_up_to_date ? (
              <Badge variant="success" icon={FiCheckCircle}>
                Up to Date
              </Badge>
            ) : documentSet.cc_pair_descriptors.length > 0 ? (
              <Badge variant="in_progress" icon={FiClock}>
                Syncing
              </Badge>
            ) : (
              <Badge variant="destructive" icon={FiAlertTriangle}>
                Deleting
              </Badge>
            )}
          </div>
        </div>

        {/* Actions Column */}
        <div className="flex items-center justify-between">
          {/* Public/Private Badge */}
          <div className="text-center">
            <p className="text-sm text-text-500 mb-1">Access</p>
            <div className="flex justify-center">
              {documentSet.is_public ? (
                <Badge
                  variant={isEditable ? "success" : "default"}
                  icon={FiUnlock}
                >
                  Public
                </Badge>
              ) : (
                <Badge
                  variant={isEditable ? "private" : "default"}
                  icon={FiLock}
                >
                  Private
                </Badge>
              )}
            </div>
          </div>

          {/* Delete Button */}
          <div className="text-center">
            <p className="text-sm text-text-500 mb-1">Delete</p>
            {isEditable ? (
              <DeleteButton
                onClick={async () => {
                  const response = await deleteDocumentSet(documentSet.id);
                  if (response.ok) {
                    setPopup({
                      message: `Document set "${documentSet.name}" scheduled for deletion`,
                      type: "success",
                    });
                  } else {
                    const errorMsg = (await response.json()).detail;
                    setPopup({
                      message: `Failed to schedule document set for deletion - ${errorMsg}`,
                      type: "error",
                    });
                  }
                  refresh();
                  refreshEditable();
                }}
              />
            ) : (
              <span className="text-text-500">-</span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

interface TeamGroupedDocumentSetsTableProps {
  documentSets: DocumentSet[];
  editableDocumentSets: DocumentSet[];
  refresh: () => void;
  refreshEditable: () => void;
  setPopup: (popupSpec: PopupSpec | null) => void;
}

export function TeamGroupedDocumentSetsTable({
  documentSets,
  editableDocumentSets,
  refresh,
  refreshEditable,
  setPopup,
}: TeamGroupedDocumentSetsTableProps) {
  const { user } = useUser();

  // Create a set of editable document set IDs for quick lookup
  const editableDocumentSetIds = useMemo(() => {
    return new Set(editableDocumentSets.map(ds => ds.id));
  }, [editableDocumentSets]);

  // Filter document sets based on user role (same logic as original)
  const filteredDocumentSets = useMemo(() => {
    let filtered = documentSets;
    
    if (user?.role === UserRole.TEAM_ADMIN) {
      // TEAM_ADMIN users should not see public document sets
      filtered = documentSets.filter(docSet => !docSet.is_public);
    }
    
    return filtered;
  }, [documentSets, user?.role]);

  // Convert document sets to match TeamGroupedItem interface
  const documentSetsWithTeamData: DocumentSetWithTeamData[] = useMemo(() => {
    return filteredDocumentSets.map(docSet => ({
      ...docSet,
      // Ensure the interface requirements are met
      id: docSet.id,
      user_teams: docSet.user_teams,
      is_public: docSet.is_public,
    }));
  }, [filteredDocumentSets]);

  // Use the team grouping hook
  const { publicItems, teamGroups, teamsMap, isLoading: teamsLoading } = useTeamGroupedData(documentSetsWithTeamData);

  // Render function for individual document set items
  const renderDocumentSetItem = (documentSet: DocumentSetWithTeamData, index: number) => {
    const isEditable = editableDocumentSetIds.has(documentSet.id);
    
    return (
      <DocumentSetRow
        key={documentSet.id}
        documentSet={documentSet}
        isEditable={isEditable}
        setPopup={setPopup}
        refresh={refresh}
        refreshEditable={refreshEditable}
      />
    );
  };

  if (teamsLoading) {
    return <div className="text-center py-8">Loading teams...</div>;
  }

  return (
    <TeamGroupedView
      items={documentSetsWithTeamData}
      teams={Array.from(teamsMap.values())}
      renderItem={renderDocumentSetItem}
      emptyMessage="No document sets found"
      showPublicSection={user?.role === UserRole.ADMIN} // Only show public section for admins
      defaultExpandedTeams={[]} // Start with all sections collapsed
      enableSearch={true}
      enableTeamFilter={true}
      searchPlaceholder="Search document sets by name..."
    />
  );
}
