"use client";

import React, { useState, useMemo } from "react";
import Text from "@/components/ui/text";
import { Persona } from "./interfaces";
import { useRouter } from "next/navigation";
import { CustomCheckbox } from "@/components/CustomCheckbox";
import { usePopup } from "@/components/admin/connectors/Popup";
import { FiEdit2 } from "react-icons/fi";
import { TrashIcon } from "@/components/icons/icons";
import { useUser } from "@/components/user/UserProvider";
import { useAssistants } from "@/components/context/AssistantsContext";
import { ConfirmEntityModal } from "@/components/modals/ConfirmEntityModal";
import { UserRole } from "@/lib/types";
import { TeamGroupedView, TeamGroupedItem } from "@/components/admin/TeamGroupedView";
import { useTeamGroupedData } from "@/hooks/useTeamGroupedData";
import {
  deletePersona,
  togglePersonaDefault,
  togglePersonaVisibility,
} from "./lib";


// Extend Persona to match TeamGroupedItem interface
interface PersonaWithTeamData extends Persona, TeamGroupedItem {
  id: number; // Override to resolve type conflict between DocumentSet.id (number) and TeamGroupedItem.id (string | number)
  is_public: boolean; // Override to resolve type conflict between DocumentSet.is_public (boolean) and TeamGroupedItem.is_public (boolean | undefined)
  user_teams: number[]; 
}

function PersonaTypeDisplay({ persona }: { persona: Persona }) {
  if (persona.builtin_persona) {
    return <Text>Built-In</Text>;
  }

  if (persona.is_default_persona) {
    return <Text>Default</Text>;
  }

  if (persona.is_public) {
    return <Text>Public</Text>;
  }

  if (persona.groups.length > 0 || persona.user_teams.length > 0 || persona.users.length > 0) {
    return <Text>Shared</Text>;
  }

  return <Text>Personal {persona.owner && <>({persona.owner.email})</>}</Text>;
}

export function TeamGroupedPersonasTable() {
  const router = useRouter();
  const { popup, setPopup } = usePopup();
  const { refreshUser, isAdmin, user } = useUser();
  const {
    allAssistants: assistants,
    refreshAssistants,
    editablePersonas,
  } = useAssistants();

  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [personaToDelete, setPersonaToDelete] = useState<Persona | null>(null);
  const [defaultModalOpen, setDefaultModalOpen] = useState(false);
  const [personaToToggleDefault, setPersonaToToggleDefault] = useState<Persona | null>(null);

  const editablePersonaIds = useMemo(() => {
    return new Set(editablePersonas.map((p) => p.id.toString()));
  }, [editablePersonas]);

  // Filter personas based on user role (same logic as original)
  const filteredPersonas = useMemo(() => {
    let filtered = assistants;
    
    if (user?.role === UserRole.TEAM_ADMIN) {
      // TEAM_ADMIN users should not see public personas
      filtered = assistants.filter(persona => !persona.is_public);
    }
    
    return filtered;
  }, [assistants, user?.role]);

  // Convert personas to match TeamGroupedItem interface
  const personasWithTeamData: PersonaWithTeamData[] = useMemo(() => {
    return filteredPersonas.map(persona => ({
      ...persona,
      // Ensure the interface requirements are met
      id: persona.id,
      user_teams: persona.user_teams,
      is_public: persona.is_public,
    }));
  }, [filteredPersonas]);

  // Use the team grouping hook
  const { publicItems, teamGroups, teamsMap, isLoading: teamsLoading } = useTeamGroupedData(personasWithTeamData);

  const openDeleteModal = (persona: Persona) => {
    setPersonaToDelete(persona);
    setDeleteModalOpen(true);
  };

  const closeDeleteModal = () => {
    setDeleteModalOpen(false);
    setPersonaToDelete(null);
  };

  const handleDeletePersona = async () => {
    if (personaToDelete) {
      const response = await deletePersona(personaToDelete.id);
      if (response.ok) {
        await refreshAssistants();
        closeDeleteModal();
      } else {
        setPopup({
          type: "error",
          message: `Failed to delete persona - ${await response.text()}`,
        });
      }
    }
  };

  const openDefaultModal = (persona: Persona) => {
    setPersonaToToggleDefault(persona);
    setDefaultModalOpen(true);
  };

  const closeDefaultModal = () => {
    setDefaultModalOpen(false);
    setPersonaToToggleDefault(null);
  };

  const handleToggleDefault = async () => {
    if (personaToToggleDefault) {
      const response = await togglePersonaDefault(
        personaToToggleDefault.id,
        personaToToggleDefault.is_default_persona
      );
      if (response.ok) {
        await refreshAssistants();
        closeDefaultModal();
      } else {
        setPopup({
          type: "error",
          message: `Failed to update persona - ${await response.text()}`,
        });
      }
    }
  };

  // Render function for individual persona items
  const renderPersonaItem = (persona: PersonaWithTeamData, index: number) => {
    const isEditable = editablePersonaIds.has(persona.id.toString());
    
    return (
      <div className="border border-border rounded-lg p-4 bg-background-50 dark:bg-neutral-900">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
          {/* Name Column */}
          <div className="flex items-center">
            {!persona.builtin_persona && (
              <FiEdit2
                className="mr-2 cursor-pointer text-text-500 hover:text-text-700"
                onClick={() =>
                  router.push(
                    `/assistants/edit/${persona.id}?u=${Date.now()}&admin=true`
                  )
                }
              />
            )}
            <p className="font-medium text-text-900">{persona.name}</p>
          </div>

          {/* Description Column */}
          <div className="lg:col-span-2">
            <p className="text-sm text-text-700 break-words">{persona.description}</p>
          </div>

          {/* Actions Column */}
          <div className="flex items-center justify-end gap-4">
            {/* Type Display */}
            <PersonaTypeDisplay persona={persona} />

            {/* Visibility Toggle */}
            <div
              onClick={async () => {
                if (isEditable) {
                  const response = await togglePersonaVisibility(
                    persona.id,
                    persona.is_visible
                  );
                  if (response.ok) {
                    await refreshAssistants();
                  } else {
                    setPopup({
                      type: "error",
                      message: `Failed to update persona - ${await response.text()}`,
                    });
                  }
                }
              }}
              className={`flex items-center gap-1 px-2 py-1 rounded ${
                isEditable
                  ? "hover:bg-background-100 cursor-pointer"
                  : ""
              }`}
            >
              <span className="text-sm">
                {persona.is_visible ? "Visible" : "Hidden"}
              </span>
              <CustomCheckbox checked={persona.is_visible} />
            </div>

            {/* Delete Button */}
            {!persona.builtin_persona && isEditable ? (
              <div
                className="hover:bg-background-100 rounded p-1 cursor-pointer"
                onClick={() => openDeleteModal(persona)}
              >
                <TrashIcon className="w-4 h-4 text-error" />
              </div>
            ) : (
              <div className="w-6 h-6" /> // Placeholder for alignment
            )}
          </div>
        </div>
      </div>
    );
  };

  if (teamsLoading) {
    return <div className="text-center py-8">Loading teams...</div>;
  }

  return (
    <div>
      {popup}
      
      {/* Delete Modal */}
      {deleteModalOpen && personaToDelete && (
        <ConfirmEntityModal
          entityType="Assistant"
          entityName={personaToDelete.name}
          onClose={closeDeleteModal}
          onSubmit={handleDeletePersona}
        />
      )}

      {/* Default Modal */}
      {defaultModalOpen && personaToToggleDefault && (
        <ConfirmEntityModal
          variant="action"
          entityType="Assistant"
          entityName={personaToToggleDefault.name}
          onClose={closeDefaultModal}
          onSubmit={handleToggleDefault}
          actionButtonText={
            personaToToggleDefault.is_default_persona
              ? "Remove Featured"
              : "Set as Featured"
          }
          additionalDetails={
            personaToToggleDefault.is_default_persona
              ? `Removing "${personaToToggleDefault.name}" as a featured assistant will not affect its visibility or accessibility.`
              : `Setting "${personaToToggleDefault.name}" as a featured assistant will make it public and visible to all users. This action cannot be undone.`
          }
        />
      )}

      {/* Team Grouped View */}
      <TeamGroupedView
        items={personasWithTeamData}
        teams={Array.from(teamsMap.values())}
        renderItem={renderPersonaItem}
        emptyMessage="No assistants found"
        showPublicSection={isAdmin} // Only show public section for admins
        defaultExpandedTeams={[]} // Start with all sections collapsed
        enableSearch={true}
        enableTeamFilter={true}
        searchPlaceholder="Search assistants by name or description..."
      />
    </div>
  );
}
