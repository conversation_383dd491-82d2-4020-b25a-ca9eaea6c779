"use client";

import { PersonasTable } from "./PersonaTable";
import { TeamGroupedPersonasTable } from "./TeamGroupedPersonasTable";
import Text from "@/components/ui/text";
import Title from "@/components/ui/title";
import { Separator } from "@/components/ui/separator";
import { AssistantsIcon } from "@/components/icons/icons";
import { AdminPageTitle } from "@/components/admin/Title";
import { SubLabel } from "@/components/admin/connectors/Field";
import CreateButton from "@/components/ui/createButton";
import { useUser } from "@/components/user/UserProvider";
import { UserRole } from "@/lib/types";

export default function Page() {
  const { isAdmin } = useUser();

  return (
    <div className="mx-auto container">
      <AdminPageTitle icon={<AssistantsIcon size={32} />} title="Assistants" />

      <Text className="mb-2">
        Assistants are a way to build custom search/question-answering
        experiences for different use cases.
      </Text>
      <Text className="mt-2">They allow you to customize:</Text>
      <div className="text-sm">
        <ul className="list-disc mt-2 ml-4">
          <li>
            The prompt used by your LLM of choice to respond to the user query
          </li>
          <li>The documents that are used as context</li>
        </ul>
      </div>

      <div>
        <Separator />

        <Title>Create an Assistant</Title>
        <CreateButton href="/assistants/new?admin=true" text="New Assistant" />

        <Separator />

        <Title>Existing Assistants</Title>
        <SubLabel>
          {isAdmin
            ? "Assistants are organized by teams below. Public assistants are visible to all users, while team-specific assistants are only visible to team members."
            : "Assistants will be displayed as options on the Chat / Search interfaces in the order they are displayed below. Assistants marked as hidden will not be displayed. Editable assistants are shown at the top."
          }
        </SubLabel>

        {/* Use team-grouped view for admins, original table for team admins */}
        {isAdmin ? <TeamGroupedPersonasTable /> : <PersonasTable />}
      </div>
    </div>
  );
}
