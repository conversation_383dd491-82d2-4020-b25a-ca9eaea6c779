import { useMemo } from "react";
import { useUserTeams } from "@/lib/hooks";
import { UserTeams } from "@/lib/types";

export interface TeamGroupedItem {
  id: string | number;
  user_teams?: number[];
  is_public?: boolean;
  [key: string]: any;
}

export interface GroupedData<T extends TeamGroupedItem> {
  publicItems: T[];
  teamGroups: Array<{
    team: UserTeams;
    items: T[];
  }>;
  teamsMap: Map<number, UserTeams>;
}

/**
 * Custom hook to group items by teams and provide team information
 * @param items - Array of items that have user_teams and is_public properties
 * @returns Grouped data with public items, team groups, and teams map
 */
export function useTeamGroupedData<T extends TeamGroupedItem>(
  items: T[]
): GroupedData<T> & {
  isLoading: boolean;
  error: string;
  refreshTeams: () => void;
} {
  const { data: teams, isLoading, error, refreshUserTeams } = useUserTeams();

  const groupedData = useMemo(() => {
    if (!teams || !items) {
      return {
        publicItems: [],
        teamGroups: [],
        teamsMap: new Map(),
      };
    }

    // Create a map for quick team lookup
    const teamsMap = new Map(teams.map(team => [team.id, team]));

    // Separate public items
    const publicItems = items.filter(item => item.is_public);

    // Group items by teams
    const teamItemsMap = new Map<number, T[]>();
    
    // Initialize team groups
    teams.forEach(team => {
      teamItemsMap.set(team.id, []);
    });

    // Categorize non-public items by teams
    items.forEach(item => {
      if (!item.is_public && item.user_teams && item.user_teams.length > 0) {
        item.user_teams.forEach(teamId => {
          const teamItems = teamItemsMap.get(teamId);
          if (teamItems) {
            teamItems.push(item);
          }
        });
      }
    });

    // Create team groups with items
    const teamGroups = teams
      .map(team => ({
        team,
        items: teamItemsMap.get(team.id) || [],
      }))
      .filter(group => group.items.length > 0); // Only include teams with items

    return {
      publicItems,
      teamGroups,
      teamsMap,
    };
  }, [items, teams]);

  return {
    ...groupedData,
    isLoading,
    error,
    refreshTeams: refreshUserTeams,
  };
}

/**
 * Utility function to get team names for an item
 * @param item - Item with user_teams property
 * @param teamsMap - Map of team ID to team data
 * @returns Array of team names
 */
export function getTeamNamesForItem(
  item: TeamGroupedItem,
  teamsMap: Map<number, UserTeams>
): string[] {
  if (!item.user_teams) return [];
  
  return item.user_teams
    .map(teamId => teamsMap.get(teamId)?.name)
    .filter((name): name is string => Boolean(name));
}

/**
 * Utility function to check if user has access to view an item based on their teams
 * @param item - Item to check access for
 * @param userTeamIds - Array of team IDs the user belongs to
 * @param isAdmin - Whether the user is an admin
 * @returns Boolean indicating if user has access
 */
export function hasAccessToItem(
  item: TeamGroupedItem,
  userTeamIds: number[],
  isAdmin: boolean
): boolean {
  // Admins can see everything
  if (isAdmin) return true;
  
  // Public items are visible to everyone
  if (item.is_public) return true;
  
  // Check if user belongs to any of the item's teams
  if (item.user_teams && item.user_teams.length > 0) {
    return item.user_teams.some(teamId => userTeamIds.includes(teamId));
  }
  
  // If item has no teams assigned, it's not accessible to non-admin users
  return false;
}

/**
 * Hook specifically for filtering items based on user access
 * @param items - Array of items to filter
 * @param userTeamIds - Array of team IDs the user belongs to
 * @param isAdmin - Whether the user is an admin
 * @returns Filtered array of items the user has access to
 */
export function useAccessibleItems<T extends TeamGroupedItem>(
  items: T[],
  userTeamIds: number[],
  isAdmin: boolean
): T[] {
  return useMemo(() => {
    return items.filter(item => hasAccessToItem(item, userTeamIds, isAdmin));
  }, [items, userTeamIds, isAdmin]);
}
